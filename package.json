{"name": "complete-telegram-bot-system", "version": "1.0.0", "description": "Complete enterprise Telegram bot management system with React frontend and Node.js backend", "main": "backend/server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && nodemon server.js", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "start": "cd backend && node server.js", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "setup": "npm run install-all && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["telegram", "bot", "management", "react", "nodejs", "enterprise", "scalable"], "author": "Enterprise Bot System", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "nodemon": "^2.0.20"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}