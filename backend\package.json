{"name": "telegram-bot-backend", "version": "1.0.0", "description": "Backend server for Telegram bot management system", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"node-telegram-bot-api": "^0.61.0", "openai": "^3.3.0", "axios": "^1.4.0", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "winston": "^3.8.2", "dotenv": "^16.0.3", "fs": "^0.0.1-security", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^2.0.20"}, "keywords": ["telegram", "bot", "api", "nodejs", "express"], "author": "Enterprise Bot System", "license": "MIT", "engines": {"node": ">=18.0.0"}}