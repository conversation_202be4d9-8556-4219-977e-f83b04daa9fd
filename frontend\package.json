{"name": "telegram-bot-frontend", "version": "1.0.0", "description": "React frontend for Telegram bot management system", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "@heroicons/react": "^2.0.16", "recharts": "^2.5.0", "date-fns": "^2.29.3", "clsx": "^1.2.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.1.0", "tailwindcss": "^3.2.7", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4"}, "keywords": ["react", "telegram", "bot", "management", "frontend"], "author": "Enterprise Bot System", "license": "MIT", "engines": {"node": ">=18.0.0"}}